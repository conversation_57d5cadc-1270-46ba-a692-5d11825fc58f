'use client';

import React, { useState, useEffect } from 'react';
import { ProjectPageResponse, OCRJobResponse, LLMProvider } from '@/types/api';
import { ocrAPI } from '@/lib/api-client';
import { LoadingOverlay, Spinner } from '@/components/feedback/LoadingStates';
import { ErrorDisplay } from '@/components/feedback/ErrorDisplay';

interface BatchOCRProcessorProps {
  projectId: string;
  availablePages: ProjectPageResponse[];
  onJobsCreated?: (jobs: OCRJobResponse[]) => void;
  onClose?: () => void;
  className?: string;
}

export const BatchOCRProcessor: React.FC<BatchOCRProcessorProps> = ({
  projectId,
  availablePages,
  onJobsCreated,
  onClose,
  className = ''
}) => {
  const [selectedPages, setSelectedPages] = useState<string[]>([]);
  const [selectedProvider, setSelectedProvider] = useState<LLMProvider>(LLMProvider.CLAUDE);
  const [customPrompt, setCustomPrompt] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [jobs, setJobs] = useState<OCRJobResponse[]>([]);

  const handlePageToggle = (pageId: string) => {
    setSelectedPages(prev => 
      prev.includes(pageId) 
        ? prev.filter(id => id !== pageId)
        : [...prev, pageId]
    );
  };

  const handleSelectAll = () => {
    if (selectedPages.length === availablePages.length) {
      setSelectedPages([]);
    } else {
      setSelectedPages(availablePages.map(page => page.id));
    }
  };

  const handleStartBatchProcessing = async () => {
    if (selectedPages.length === 0) {
      setError('Please select at least one page to process');
      return;
    }

    try {
      setIsProcessing(true);
      setError(null);

      const batchRequest = {
        project_id: projectId,
        page_ids: selectedPages,
        provider: selectedProvider,
        custom_prompt: customPrompt || undefined
      };

      const createdJobs = await ocrAPI.processBatchOCR(batchRequest);
      setJobs(createdJobs);
      onJobsCreated?.(createdJobs);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start batch OCR processing');
    } finally {
      setIsProcessing(false);
    }
  };

  const pagesWithoutOCR = availablePages.filter(page => 
    !page.text_region_count || page.text_region_count === 0
  );

  return (
    <div className={`bg-white rounded-lg shadow-lg ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-gray-200">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Batch OCR Processing</h3>
          <p className="text-gray-600 mt-1">Process multiple pages for text detection</p>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
            title="Close"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
      </div>

      <div className="p-6 space-y-6">
        {/* Provider Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            LLM Provider
          </label>
          <select
            value={selectedProvider}
            onChange={(e) => setSelectedProvider(e.target.value as LLMProvider)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            disabled={isProcessing}
          >
            <option value={LLMProvider.CLAUDE}>Claude (Anthropic)</option>
            <option value={LLMProvider.OPENAI}>OpenAI GPT</option>
            <option value={LLMProvider.GEMINI}>Google Gemini</option>
            <option value={LLMProvider.DEEPSEEK}>Deepseek</option>
          </select>
        </div>

        {/* Custom Prompt */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Custom Prompt (Optional)
          </label>
          <textarea
            value={customPrompt}
            onChange={(e) => setCustomPrompt(e.target.value)}
            placeholder="Enter custom instructions for OCR processing..."
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            rows={3}
            disabled={isProcessing}
          />
        </div>

        {/* Page Selection */}
        <div>
          <div className="flex items-center justify-between mb-3">
            <label className="text-sm font-medium text-gray-700">
              Select Pages ({selectedPages.length}/{availablePages.length})
            </label>
            <button
              onClick={handleSelectAll}
              className="text-sm text-blue-600 hover:text-blue-700"
              disabled={isProcessing}
            >
              {selectedPages.length === availablePages.length ? 'Deselect All' : 'Select All'}
            </button>
          </div>

          {/* Quick filters */}
          <div className="flex space-x-2 mb-3">
            <button
              onClick={() => setSelectedPages(pagesWithoutOCR.map(p => p.id))}
              className="text-xs px-3 py-1 bg-blue-100 text-blue-700 rounded-full hover:bg-blue-200"
              disabled={isProcessing}
            >
              Pages without OCR ({pagesWithoutOCR.length})
            </button>
          </div>

          {/* Page Grid */}
          <div className="max-h-64 overflow-y-auto border border-gray-200 rounded-lg">
            {availablePages.length === 0 ? (
              <div className="p-4 text-center text-gray-500">
                No pages available for processing
              </div>
            ) : (
              <div className="grid grid-cols-1 gap-2 p-3">
                {availablePages.map((page) => (
                  <label
                    key={page.id}
                    className={`flex items-center space-x-3 p-3 rounded-lg cursor-pointer transition-colors ${
                      selectedPages.includes(page.id)
                        ? 'bg-blue-50 border border-blue-200'
                        : 'hover:bg-gray-50 border border-transparent'
                    }`}
                  >
                    <input
                      type="checkbox"
                      checked={selectedPages.includes(page.id)}
                      onChange={() => handlePageToggle(page.id)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      disabled={isProcessing}
                    />
                    <div className="flex-1">
                      <div className="text-sm font-medium text-gray-900">
                        Page {page.page_number}
                      </div>
                      <div className="text-xs text-gray-500">
                        {page.original_filename}
                        {page.text_region_count > 0 && (
                          <span className="ml-2 text-green-600">
                            ({page.text_region_count} regions detected)
                          </span>
                        )}
                      </div>
                    </div>
                  </label>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <ErrorDisplay
            message={error}
            severity="error"
            onDismiss={() => setError(null)}
          />
        )}

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3">
          {onClose && (
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              disabled={isProcessing}
            >
              Cancel
            </button>
          )}
          <button
            onClick={handleStartBatchProcessing}
            disabled={selectedPages.length === 0 || isProcessing}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isProcessing ? (
              <>
                <Spinner size="sm" className="mr-2" />
                Processing...
              </>
            ) : (
              `Start OCR (${selectedPages.length} pages)`
            )}
          </button>
        </div>

        {/* Jobs Created */}
        {jobs.length > 0 && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="text-sm font-medium text-green-800 mb-2">
              ✅ Batch OCR Started Successfully
            </div>
            <div className="text-sm text-green-700">
              Created {jobs.length} OCR jobs. You can monitor their progress in the Statistics tab.
            </div>
          </div>
        )}
      </div>

      {/* Loading Overlay */}
      {isProcessing && (
        <LoadingOverlay
          isVisible={true}
          message="Starting batch OCR processing..."
        />
      )}
    </div>
  );
};
