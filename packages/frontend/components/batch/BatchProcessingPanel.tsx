'use client';

import React, { useState } from 'react';
import { ProjectPageResponse, TextRegionResponse, OCRJobResponse, TranslationJobResponse } from '@/types/api';
import { BatchOCRProcessor } from './BatchOCRProcessor';
import { BatchTranslationProcessor } from './BatchTranslationProcessor';
import { BatchJobMonitor } from './BatchJobMonitor';

interface BatchProcessingPanelProps {
  projectId: string;
  availablePages: ProjectPageResponse[];
  textRegions: TextRegionResponse[];
  onClose?: () => void;
  className?: string;
}

type ActiveTab = 'ocr' | 'translation' | 'monitor';

export const BatchProcessingPanel: React.FC<BatchProcessingPanelProps> = ({
  projectId,
  availablePages,
  textRegions,
  onClose,
  className = ''
}) => {
  const [activeTab, setActiveTab] = useState<ActiveTab>('ocr');
  const [recentJobs, setRecentJobs] = useState<{
    ocr: OCRJobResponse[];
    translation: TranslationJobResponse[];
  }>({
    ocr: [],
    translation: []
  });

  const handleOCRJobsCreated = (jobs: OCRJobResponse[]) => {
    setRecentJobs(prev => ({
      ...prev,
      ocr: [...jobs, ...prev.ocr]
    }));
    // Switch to monitor tab to show progress
    setActiveTab('monitor');
  };

  const handleTranslationJobsCreated = (jobs: TranslationJobResponse[]) => {
    setRecentJobs(prev => ({
      ...prev,
      translation: [...jobs, ...prev.translation]
    }));
    // Switch to monitor tab to show progress
    setActiveTab('monitor');
  };

  const pagesWithoutOCR = availablePages.filter(page => 
    !page.text_region_count || page.text_region_count === 0
  );

  const regionsWithText = textRegions.filter(region => region.original_text);
  const regionsWithoutTranslation = regionsWithText.filter(region => !region.translated_text);

  const getTabIcon = (tab: ActiveTab) => {
    switch (tab) {
      case 'ocr':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
          </svg>
        );
      case 'translation':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
          </svg>
        );
      case 'monitor':
        return (
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
        );
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-lg ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-gray-200">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Batch Processing</h2>
          <p className="text-gray-600 mt-1">Process multiple pages and text regions efficiently</p>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
            title="Close Batch Processing"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
      </div>

      {/* Quick Stats */}
      <div className="p-6 border-b border-gray-200 bg-gray-50">
        <div className="grid grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{availablePages.length}</div>
            <div className="text-sm text-gray-600">Total Pages</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">{pagesWithoutOCR.length}</div>
            <div className="text-sm text-gray-600">Need OCR</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{regionsWithText.length}</div>
            <div className="text-sm text-gray-600">Text Regions</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">{regionsWithoutTranslation.length}</div>
            <div className="text-sm text-gray-600">Need Translation</div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex">
          <button
            onClick={() => setActiveTab('ocr')}
            className={`flex items-center space-x-2 px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'ocr'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            {getTabIcon('ocr')}
            <span>Batch OCR</span>
            {pagesWithoutOCR.length > 0 && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                {pagesWithoutOCR.length}
              </span>
            )}
          </button>
          
          <button
            onClick={() => setActiveTab('translation')}
            className={`flex items-center space-x-2 px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'translation'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            {getTabIcon('translation')}
            <span>Batch Translation</span>
            {regionsWithoutTranslation.length > 0 && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                {regionsWithoutTranslation.length}
              </span>
            )}
          </button>
          
          <button
            onClick={() => setActiveTab('monitor')}
            className={`flex items-center space-x-2 px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
              activeTab === 'monitor'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            {getTabIcon('monitor')}
            <span>Job Monitor</span>
            {(recentJobs.ocr.length + recentJobs.translation.length) > 0 && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                {recentJobs.ocr.length + recentJobs.translation.length}
              </span>
            )}
          </button>
        </nav>
      </div>

      {/* Tab Content */}
      <div className="p-6">
        {activeTab === 'ocr' && (
          <BatchOCRProcessor
            projectId={projectId}
            availablePages={availablePages}
            onJobsCreated={handleOCRJobsCreated}
            className="border-0 shadow-none p-0"
          />
        )}

        {activeTab === 'translation' && (
          <BatchTranslationProcessor
            textRegions={textRegions}
            onJobsCreated={handleTranslationJobsCreated}
            className="border-0 shadow-none p-0"
          />
        )}

        {activeTab === 'monitor' && (
          <BatchJobMonitor
            projectId={projectId}
            className="border-0 shadow-none p-0"
          />
        )}
      </div>

      {/* Quick Actions Footer */}
      <div className="border-t border-gray-200 p-4 bg-gray-50">
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600">
            💡 Tip: Use batch processing to handle multiple items efficiently
          </div>
          <div className="flex space-x-2">
            {pagesWithoutOCR.length > 0 && activeTab !== 'ocr' && (
              <button
                onClick={() => setActiveTab('ocr')}
                className="text-sm px-3 py-1 bg-orange-100 text-orange-700 rounded-full hover:bg-orange-200 transition-colors"
              >
                Process {pagesWithoutOCR.length} pages
              </button>
            )}
            {regionsWithoutTranslation.length > 0 && activeTab !== 'translation' && (
              <button
                onClick={() => setActiveTab('translation')}
                className="text-sm px-3 py-1 bg-purple-100 text-purple-700 rounded-full hover:bg-purple-200 transition-colors"
              >
                Translate {regionsWithoutTranslation.length} regions
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
