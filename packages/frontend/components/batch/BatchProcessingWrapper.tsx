'use client';

import React, { useState, useEffect } from 'react';
import { ProjectPageResponse, TextRegionResponse } from '@/types/api';
import { projectsAPI } from '@/lib/api-client';
import { BatchProcessingPanel } from './BatchProcessingPanel';
import { LoadingOverlay, Spinner } from '@/components/feedback/LoadingStates';
import { ErrorDisplay } from '@/components/feedback/ErrorDisplay';

interface BatchProcessingWrapperProps {
  projectId: string;
  textRegions: TextRegionResponse[];
  className?: string;
}

export const BatchProcessingWrapper: React.FC<BatchProcessingWrapperProps> = ({
  projectId,
  textRegions,
  className = ''
}) => {
  const [projectPages, setProjectPages] = useState<ProjectPageResponse[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load project pages
  useEffect(() => {
    const loadProjectPages = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const pagesResponse = await projectsAPI.getProjectPages(projectId);
        setProjectPages(pagesResponse.items);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load project pages');
      } finally {
        setIsLoading(false);
      }
    };

    if (projectId) {
      loadProjectPages();
    }
  }, [projectId]);

  if (isLoading) {
    return (
      <div className={`flex items-center justify-center h-full ${className}`}>
        <div className="text-center">
          <Spinner size="lg" className="mb-4" />
          <p className="text-gray-600">Loading project pages...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`p-6 ${className}`}>
        <ErrorDisplay
          message={error}
          severity="error"
          onRetry={() => window.location.reload()}
        />
      </div>
    );
  }

  return (
    <BatchProcessingPanel
      projectId={projectId}
      availablePages={projectPages}
      textRegions={textRegions}
      className={className}
    />
  );
};
