'use client';

import React, { useState, useEffect } from 'react';
import { TextRegionResponse, TranslationJobResponse, LLMProvider } from '@/types/api';
import { translationAPI } from '@/lib/api-client';
import { LoadingOverlay, Spinner } from '@/components/feedback/LoadingStates';
import { ErrorDisplay } from '@/components/feedback/ErrorDisplay';

interface BatchTranslationProcessorProps {
  textRegions: TextRegionResponse[];
  sourceLanguage?: string;
  targetLanguage?: string;
  onJobsCreated?: (jobs: TranslationJobResponse[]) => void;
  onClose?: () => void;
  className?: string;
}

export const BatchTranslationProcessor: React.FC<BatchTranslationProcessorProps> = ({
  textRegions,
  sourceLanguage = 'japanese',
  targetLanguage = 'english',
  onJobsCreated,
  onClose,
  className = ''
}) => {
  const [selectedRegions, setSelectedRegions] = useState<string[]>([]);
  const [selectedProvider, setSelectedProvider] = useState<LLMProvider>(LLMProvider.CLAUDE);
  const [customPrompt, setCustomPrompt] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [jobs, setJobs] = useState<TranslationJobResponse[]>([]);

  const handleRegionToggle = (regionId: string) => {
    setSelectedRegions(prev => 
      prev.includes(regionId) 
        ? prev.filter(id => id !== regionId)
        : [...prev, regionId]
    );
  };

  const handleSelectAll = () => {
    if (selectedRegions.length === textRegions.length) {
      setSelectedRegions([]);
    } else {
      setSelectedRegions(textRegions.map(region => region.id));
    }
  };

  const handleStartBatchProcessing = async () => {
    if (selectedRegions.length === 0) {
      setError('Please select at least one text region to translate');
      return;
    }

    try {
      setIsProcessing(true);
      setError(null);

      const batchRequest = {
        text_region_ids: selectedRegions,
        provider: selectedProvider,
        source_language: sourceLanguage,
        target_language: targetLanguage,
        custom_prompt: customPrompt || undefined
      };

      const createdJobs = await translationAPI.processBatchTranslation(batchRequest);
      setJobs(createdJobs);
      onJobsCreated?.(createdJobs);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start batch translation processing');
    } finally {
      setIsProcessing(false);
    }
  };

  const regionsWithText = textRegions.filter(region => region.original_text);
  const regionsWithoutTranslation = regionsWithText.filter(region => !region.translated_text);

  const getRegionTypeColor = (type: string) => {
    switch (type) {
      case 'speech_bubble': return 'bg-blue-100 text-blue-800';
      case 'thought_bubble': return 'bg-purple-100 text-purple-800';
      case 'narration': return 'bg-green-100 text-green-800';
      case 'sound_effect': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-lg ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-gray-200">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Batch Translation Processing</h3>
          <p className="text-gray-600 mt-1">Translate multiple text regions at once</p>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
            title="Close"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
      </div>

      <div className="p-6 space-y-6">
        {/* Language Settings */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Source Language
            </label>
            <select
              value={sourceLanguage}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50"
              disabled
            >
              <option value="japanese">Japanese</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Target Language
            </label>
            <select
              value={targetLanguage}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50"
              disabled
            >
              <option value="english">English</option>
              <option value="indonesia">Indonesian</option>
            </select>
          </div>
        </div>

        {/* Provider Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            LLM Provider
          </label>
          <select
            value={selectedProvider}
            onChange={(e) => setSelectedProvider(e.target.value as LLMProvider)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            disabled={isProcessing}
          >
            <option value={LLMProvider.CLAUDE}>Claude (Anthropic)</option>
            <option value={LLMProvider.OPENAI}>OpenAI GPT</option>
            <option value={LLMProvider.GEMINI}>Google Gemini</option>
            <option value={LLMProvider.DEEPSEEK}>Deepseek</option>
          </select>
        </div>

        {/* Custom Prompt */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Custom Prompt (Optional)
          </label>
          <textarea
            value={customPrompt}
            onChange={(e) => setCustomPrompt(e.target.value)}
            placeholder="Enter custom instructions for translation..."
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            rows={3}
            disabled={isProcessing}
          />
        </div>

        {/* Text Region Selection */}
        <div>
          <div className="flex items-center justify-between mb-3">
            <label className="text-sm font-medium text-gray-700">
              Select Text Regions ({selectedRegions.length}/{regionsWithText.length})
            </label>
            <button
              onClick={handleSelectAll}
              className="text-sm text-blue-600 hover:text-blue-700"
              disabled={isProcessing}
            >
              {selectedRegions.length === regionsWithText.length ? 'Deselect All' : 'Select All'}
            </button>
          </div>

          {/* Quick filters */}
          <div className="flex space-x-2 mb-3">
            <button
              onClick={() => setSelectedRegions(regionsWithoutTranslation.map(r => r.id))}
              className="text-xs px-3 py-1 bg-blue-100 text-blue-700 rounded-full hover:bg-blue-200"
              disabled={isProcessing}
            >
              Untranslated ({regionsWithoutTranslation.length})
            </button>
            <button
              onClick={() => setSelectedRegions(regionsWithText.filter(r => r.region_type === 'speech_bubble').map(r => r.id))}
              className="text-xs px-3 py-1 bg-purple-100 text-purple-700 rounded-full hover:bg-purple-200"
              disabled={isProcessing}
            >
              Speech Bubbles
            </button>
          </div>

          {/* Region List */}
          <div className="max-h-64 overflow-y-auto border border-gray-200 rounded-lg">
            {regionsWithText.length === 0 ? (
              <div className="p-4 text-center text-gray-500">
                No text regions with detected text available for translation
              </div>
            ) : (
              <div className="space-y-2 p-3">
                {regionsWithText.map((region) => (
                  <label
                    key={region.id}
                    className={`flex items-start space-x-3 p-3 rounded-lg cursor-pointer transition-colors ${
                      selectedRegions.includes(region.id)
                        ? 'bg-blue-50 border border-blue-200'
                        : 'hover:bg-gray-50 border border-transparent'
                    }`}
                  >
                    <input
                      type="checkbox"
                      checked={selectedRegions.includes(region.id)}
                      onChange={() => handleRegionToggle(region.id)}
                      className="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      disabled={isProcessing}
                    />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getRegionTypeColor(region.region_type)}`}>
                          {region.region_type.replace('_', ' ')}
                        </span>
                        {region.translated_text && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Translated
                          </span>
                        )}
                      </div>
                      <div className="text-sm text-gray-900 truncate">
                        {region.original_text}
                      </div>
                      {region.translated_text && (
                        <div className="text-sm text-gray-600 truncate mt-1">
                          → {region.translated_text}
                        </div>
                      )}
                    </div>
                  </label>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <ErrorDisplay
            message={error}
            severity="error"
            onDismiss={() => setError(null)}
          />
        )}

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3">
          {onClose && (
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              disabled={isProcessing}
            >
              Cancel
            </button>
          )}
          <button
            onClick={handleStartBatchProcessing}
            disabled={selectedRegions.length === 0 || isProcessing}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isProcessing ? (
              <>
                <Spinner size="sm" className="mr-2" />
                Processing...
              </>
            ) : (
              `Start Translation (${selectedRegions.length} regions)`
            )}
          </button>
        </div>

        {/* Jobs Created */}
        {jobs.length > 0 && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="text-sm font-medium text-green-800 mb-2">
              ✅ Batch Translation Started Successfully
            </div>
            <div className="text-sm text-green-700">
              Created {jobs.length} translation jobs. You can monitor their progress in the Statistics tab.
            </div>
          </div>
        )}
      </div>

      {/* Loading Overlay */}
      {isProcessing && (
        <LoadingOverlay
          isVisible={true}
          message="Starting batch translation processing..."
        />
      )}
    </div>
  );
};
