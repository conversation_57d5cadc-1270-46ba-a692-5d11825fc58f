'use client';

import React, { useState, useEffect } from 'react';
import { LLMProvider, LLMProvidersStatus } from '@/types/api';
import { llmProvidersAPI } from '@/lib/api-client';
import { LoadingOverlay, Spinner } from '@/components/feedback/LoadingStates';
import { ErrorDisplay } from '@/components/feedback/ErrorDisplay';

interface ModelSelectorProps {
  selectedProvider?: LLMProvider;
  selectedModel?: string;
  onProviderChange?: (provider: LLMProvider) => void;
  onModelChange?: (model: string) => void;
  className?: string;
  disabled?: boolean;
}

export const ModelSelector: React.FC<ModelSelectorProps> = ({
  selectedProvider,
  selectedModel,
  onProviderChange,
  onModelChange,
  className = '',
  disabled = false
}) => {
  const [providers, setProviders] = useState<LLMProvidersStatus | null>(null);
  const [availableModels, setAvailableModels] = useState<string[]>([]);
  const [isLoadingProviders, setIsLoadingProviders] = useState(true);
  const [isLoadingModels, setIsLoadingModels] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load providers on mount
  useEffect(() => {
    const loadProviders = async () => {
      try {
        setIsLoadingProviders(true);
        setError(null);
        const providersStatus = await llmProvidersAPI.getProvidersStatus();
        setProviders(providersStatus);

        // Auto-select first available provider if none selected
        if (!selectedProvider && providersStatus.providers.length > 0) {
          const firstAvailable = providersStatus.providers.find(p => p.is_available);
          if (firstAvailable && onProviderChange) {
            onProviderChange(firstAvailable.provider);
          }
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load providers');
      } finally {
        setIsLoadingProviders(false);
      }
    };

    loadProviders();
  }, [selectedProvider, onProviderChange]);

  // Load models when provider changes
  useEffect(() => {
    const loadModels = async () => {
      if (!selectedProvider) {
        setAvailableModels([]);
        return;
      }

      try {
        setIsLoadingModels(true);
        setError(null);
        const models = await llmProvidersAPI.getProviderModels(selectedProvider);
        setAvailableModels(models);

        // Auto-select first model if none selected
        if (!selectedModel && models.length > 0 && onModelChange) {
          onModelChange(models[0]);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load models');
        setAvailableModels([]);
      } finally {
        setIsLoadingModels(false);
      }
    };

    loadModels();
  }, [selectedProvider, selectedModel, onModelChange]);

  if (isLoadingProviders) {
    return (
      <div className={`relative ${className}`}>
        <LoadingOverlay isVisible={true} message="Loading providers..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className={className}>
        <ErrorDisplay
          title="Failed to load model information"
          message={error}
          severity="error"
          onRetry={() => window.location.reload()}
        />
      </div>
    );
  }

  if (!providers) {
    return null;
  }

  const availableProviders = providers.providers.filter(p => p.is_available);

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Provider Selection */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          LLM Provider
        </label>
        <div className="grid grid-cols-1 gap-2">
          {availableProviders.map((provider) => (
            <label
              key={provider.provider}
              className={`flex items-center justify-between p-3 border rounded-lg cursor-pointer transition-colors ${
                selectedProvider === provider.provider
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
            >
              <div className="flex items-center">
                <input
                  type="radio"
                  name="provider"
                  value={provider.provider}
                  checked={selectedProvider === provider.provider}
                  onChange={(e) => onProviderChange?.(e.target.value as LLMProvider)}
                  disabled={disabled}
                  className="mr-3"
                />
                <div>
                  <div className="font-medium text-gray-900 capitalize">
                    {provider.provider}
                  </div>
                  <div className="text-sm text-gray-500">
                    {provider.available_models.length} models available
                  </div>
                </div>
              </div>
              
              {/* Status indicator */}
              <div className="flex items-center">
                {provider.supports_vision && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 mr-2">
                    Vision
                  </span>
                )}
                <div className="w-2 h-2 bg-green-500 rounded-full" title="Available" />
              </div>
            </label>
          ))}
        </div>

        {availableProviders.length === 0 && (
          <div className="text-center py-4 text-gray-500">
            <svg className="w-8 h-8 mx-auto mb-2 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <p className="text-sm">No LLM providers available</p>
            <p className="text-xs text-gray-400 mt-1">Configure API keys to enable providers</p>
          </div>
        )}
      </div>

      {/* Model Selection */}
      {selectedProvider && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Model
            {isLoadingModels && (
              <Spinner size="sm" className="ml-2 inline-block" />
            )}
          </label>
          
          {availableModels.length > 0 ? (
            <select
              value={selectedModel || ''}
              onChange={(e) => onModelChange?.(e.target.value)}
              disabled={disabled || isLoadingModels}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <option value="">Select a model...</option>
              {availableModels.map((model) => (
                <option key={model} value={model}>
                  {model}
                </option>
              ))}
            </select>
          ) : (
            <div className="text-center py-3 text-gray-500 border border-gray-200 rounded-lg">
              <p className="text-sm">No models available for {selectedProvider}</p>
            </div>
          )}
        </div>
      )}

      {/* Provider Info */}
      {selectedProvider && providers && (
        <div className="bg-gray-50 rounded-lg p-3">
          <div className="text-sm text-gray-600">
            {(() => {
              const provider = providers.providers.find(p => p.provider === selectedProvider);
              if (!provider) return null;
              
              return (
                <div className="space-y-1">
                  <div><strong>Default Model:</strong> {provider.default_model || 'None'}</div>
                  <div><strong>Vision Support:</strong> {provider.supports_vision ? 'Yes' : 'No'}</div>
                  <div><strong>API Key:</strong> {provider.api_key_configured ? 'Configured' : 'Not configured'}</div>
                </div>
              );
            })()}
          </div>
        </div>
      )}
    </div>
  );
};
