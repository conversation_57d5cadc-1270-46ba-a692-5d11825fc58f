'use client';

import React, { useState, useEffect } from 'react';
import { ProjectDetailResponse, ProjectPageResponse } from '@/types/api';
import { projectsAPI } from '@/lib/api-client';
import { buildImageUrl, buildThumbnailUrl } from '@/lib/api-utils';
import { LoadingOverlay, Spinner } from '@/components/feedback/LoadingStates';
import { ErrorDisplay } from '@/components/feedback/ErrorDisplay';

interface ProjectDetailProps {
  projectId: string;
  onPageSelect?: (page: ProjectPageResponse) => void;
  onClose?: () => void;
  className?: string;
}

export const ProjectDetail: React.FC<ProjectDetailProps> = ({
  projectId,
  onPageSelect,
  onClose,
  className = ''
}) => {
  const [projectDetail, setProjectDetail] = useState<ProjectDetailResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load project detail
  useEffect(() => {
    const loadProjectDetail = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const detail = await projectsAPI.getProjectDetail(projectId);
        setProjectDetail(detail);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load project details');
      } finally {
        setIsLoading(false);
      }
    };

    if (projectId) {
      loadProjectDetail();
    }
  }, [projectId]);

  if (isLoading) {
    return (
      <div className={`bg-white rounded-lg shadow-lg ${className}`}>
        <LoadingOverlay isVisible={true} message="Loading project details..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
        <ErrorDisplay
          title="Failed to load project details"
          message={error}
          severity="error"
          onRetry={() => window.location.reload()}
          onDismiss={onClose}
        />
      </div>
    );
  }

  if (!projectDetail) {
    return null;
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className={`bg-white rounded-lg shadow-lg ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-gray-200">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">{projectDetail.name}</h2>
          {projectDetail.description && (
            <p className="text-gray-600 mt-1">{projectDetail.description}</p>
          )}
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
            title="Close"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
      </div>

      {/* Project Info */}
      <div className="p-6 border-b border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="text-2xl font-bold text-blue-600">{projectDetail.pages.length}</div>
            <div className="text-sm text-blue-800">Total Pages</div>
          </div>

          <div className="bg-green-50 rounded-lg p-4">
            <div className="text-2xl font-bold text-green-600">
              {projectDetail.pages.reduce((sum, page) => sum + (page.text_region_count || 0), 0)}
            </div>
            <div className="text-sm text-green-800">Text Regions</div>
          </div>

          <div className="bg-purple-50 rounded-lg p-4">
            <div className="text-lg font-semibold text-purple-600">
              {projectDetail.source_language} → {projectDetail.target_language}
            </div>
            <div className="text-sm text-purple-800">Languages</div>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <div className="text-sm font-medium text-gray-900">
              {formatDate(projectDetail.created_at)}
            </div>
            <div className="text-sm text-gray-600">Created</div>
          </div>
        </div>
      </div>

      {/* Pages Grid */}
      <div className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Pages ({projectDetail.pages.length})
        </h3>

        {projectDetail.pages.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <svg className="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <p className="text-sm">No pages uploaded yet</p>
            <p className="text-xs text-gray-400 mt-1">Upload some manga pages to get started</p>
          </div>
        ) : (
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {projectDetail.pages.map((page) => (
              <div
                key={page.id}
                onClick={() => onPageSelect?.(page)}
                className="relative group cursor-pointer rounded-lg overflow-hidden border-2 border-gray-200 hover:border-gray-300 transition-all"
              >
                {/* Thumbnail */}
                <div className="aspect-[3/4] bg-gray-100">
                  <img
                    src={buildThumbnailUrl(page.id, 200)}
                    alt={`Page ${page.page_number}`}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = '/placeholder-image.svg';
                    }}
                  />
                </div>

                {/* Page Info Overlay */}
                <div className="absolute inset-x-0 bottom-0 bg-gradient-to-t from-black/60 to-transparent p-2">
                  <div className="text-white text-xs">
                    <div className="font-medium">Page {page.page_number}</div>
                    {page.text_region_count > 0 && (
                      <div className="text-gray-200">{page.text_region_count} regions</div>
                    )}
                  </div>
                </div>

                {/* Hover overlay */}
                <div className="absolute inset-0 bg-blue-500/10 opacity-0 group-hover:opacity-100 transition-opacity" />
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
