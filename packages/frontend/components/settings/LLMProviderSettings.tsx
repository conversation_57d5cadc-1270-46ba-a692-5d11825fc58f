'use client';

import React, { useState, useEffect } from 'react';
import { LLMProvider, LLMProvidersStatus } from '@/types/api';
import { llmProvidersAPI } from '@/lib/api-client';
import { ModelSelector } from '@/components/llm/ModelSelector';
import { LoadingOverlay, Spinner } from '@/components/feedback/LoadingStates';
import { ErrorDisplay } from '@/components/feedback/ErrorDisplay';

interface LLMProviderSettingsProps {
  onClose?: () => void;
  className?: string;
}

export const LLMProviderSettings: React.FC<LLMProviderSettingsProps> = ({
  onClose,
  className = ''
}) => {
  const [providers, setProviders] = useState<LLMProvidersStatus | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedProvider, setSelectedProvider] = useState<LLMProvider | undefined>();
  const [selectedModel, setSelectedModel] = useState<string | undefined>();

  // Load providers on mount
  useEffect(() => {
    const loadProviders = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const providersStatus = await llmProvidersAPI.getProvidersStatus();
        setProviders(providersStatus);

        // Set default provider
        if (providersStatus.default_provider) {
          setSelectedProvider(providersStatus.default_provider);
        } else {
          const firstAvailable = providersStatus.providers.find(p => p.is_available);
          if (firstAvailable) {
            setSelectedProvider(firstAvailable.provider);
          }
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load provider settings');
      } finally {
        setIsLoading(false);
      }
    };

    loadProviders();
  }, []);

  if (isLoading) {
    return (
      <div className={`bg-white rounded-lg shadow-lg ${className}`}>
        <LoadingOverlay isVisible={true} message="Loading provider settings..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
        <ErrorDisplay
          title="Failed to load provider settings"
          message={error}
          severity="error"
          onRetry={() => window.location.reload()}
          onDismiss={onClose}
        />
      </div>
    );
  }

  if (!providers) {
    return null;
  }

  return (
    <div className={`bg-white rounded-lg shadow-lg ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-gray-200">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">LLM Provider Settings</h2>
          <p className="text-gray-600 mt-1">Configure language model providers for OCR and translation</p>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
            title="Close"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
      </div>

      <div className="p-6 space-y-6">
        {/* Provider Status Overview */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Provider Status</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {providers.providers.map((provider) => (
              <div
                key={provider.provider}
                className={`p-4 border rounded-lg ${
                  provider.is_available
                    ? 'border-green-200 bg-green-50'
                    : 'border-red-200 bg-red-50'
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900 capitalize">
                    {provider.provider}
                  </h4>
                  <div className={`w-3 h-3 rounded-full ${
                    provider.is_available ? 'bg-green-500' : 'bg-red-500'
                  }`} />
                </div>
                
                <div className="text-sm space-y-1">
                  <div className={`${provider.is_available ? 'text-green-700' : 'text-red-700'}`}>
                    {provider.is_available ? 'Available' : 'Not Available'}
                  </div>
                  <div className="text-gray-600">
                    API Key: {provider.api_key_configured ? 'Configured' : 'Not configured'}
                  </div>
                  <div className="text-gray-600">
                    Models: {provider.available_models.length}
                  </div>
                  <div className="text-gray-600">
                    Vision: {provider.supports_vision ? 'Yes' : 'No'}
                  </div>
                  {provider.default_model && (
                    <div className="text-gray-600">
                      Default: {provider.default_model}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Default Provider Selection */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Default Provider & Model</h3>
          <div className="bg-gray-50 rounded-lg p-4">
            <ModelSelector
              selectedProvider={selectedProvider}
              selectedModel={selectedModel}
              onProviderChange={setSelectedProvider}
              onModelChange={setSelectedModel}
            />
          </div>
        </div>

        {/* Configuration Instructions */}
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Configuration</h3>
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="text-sm text-blue-800 space-y-2">
              <p className="font-medium">To configure LLM providers, set the following environment variables:</p>
              <div className="bg-white rounded p-3 font-mono text-xs space-y-1">
                <div><strong>OpenAI:</strong> OPENAI_API_KEY=your_api_key</div>
                <div><strong>Anthropic:</strong> ANTHROPIC_API_KEY=your_api_key</div>
                <div><strong>Google:</strong> GOOGLE_API_KEY=your_api_key</div>
              </div>
              <p className="text-xs">
                Restart the backend server after setting environment variables.
              </p>
            </div>
          </div>
        </div>

        {/* Provider Details */}
        {providers.providers.length > 0 && (
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Provider Details</h3>
            <div className="space-y-4">
              {providers.providers.map((provider) => (
                <div key={provider.provider} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-medium text-gray-900 capitalize">
                      {provider.provider}
                    </h4>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      provider.is_available
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {provider.is_available ? 'Available' : 'Unavailable'}
                    </span>
                  </div>
                  
                  {provider.available_models.length > 0 ? (
                    <div>
                      <div className="text-sm font-medium text-gray-700 mb-2">
                        Available Models ({provider.available_models.length}):
                      </div>
                      <div className="flex flex-wrap gap-2">
                        {provider.available_models.map((model) => (
                          <span
                            key={model}
                            className={`inline-flex items-center px-2 py-1 rounded text-xs font-medium ${
                              model === provider.default_model
                                ? 'bg-blue-100 text-blue-800'
                                : 'bg-gray-100 text-gray-800'
                            }`}
                          >
                            {model}
                            {model === provider.default_model && (
                              <span className="ml-1 text-blue-600">★</span>
                            )}
                          </span>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <div className="text-sm text-gray-500">
                      No models available
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Summary */}
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="text-sm text-gray-700">
            <div className="font-medium mb-2">Summary:</div>
            <div className="space-y-1">
              <div>Total Providers: {providers.providers.length}</div>
              <div>Available Providers: {providers.total_available}</div>
              <div>Default Provider: {providers.default_provider || 'None'}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
