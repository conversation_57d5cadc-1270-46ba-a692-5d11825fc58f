'use client';

import React, { useState } from 'react';
import { useAppContext } from '@/store/AppContext';
import { LLMProviderSettings } from '@/components/settings/LLMProviderSettings';
import { DataManagement } from '@/components/settings/DataManagement';

interface SettingsPanelProps {
  onClose?: () => void;
  className?: string;
}

export const SettingsPanel: React.FC<SettingsPanelProps> = ({
  onClose,
  className = ''
}) => {
  const { state, dispatch } = useAppContext();
  const [activeTab, setActiveTab] = useState<'general' | 'appearance' | 'canvas' | 'providers' | 'data'>('general');

  const handlePreferenceChange = (key: string, value: any) => {
    dispatch({
      type: 'UPDATE_PREFERENCES',
      payload: { [key]: value }
    });
  };

  const handleUIChange = (key: string, value: any) => {
    dispatch({
      type: 'SET_PANEL_VISIBILITY',
      payload: { panel: key, visible: value }
    });
  };

  const resetAllSettings = () => {
    if (confirm('Are you sure you want to reset all settings to defaults? This action cannot be undone.')) {
      // Reset preferences to defaults
      dispatch({
        type: 'UPDATE_PREFERENCES',
        payload: {
          theme: 'light',
          language: 'en',
          autoSave: true,
          gridSize: 20,
          defaultFontFamily: 'Arial',
          defaultFontSize: 14,
          defaultFontColor: '#000000',
          defaultBackgroundColor: 'transparent'
        }
      });

      // Reset UI state to defaults
      dispatch({
        type: 'SET_PANEL_VISIBILITY',
        payload: { panel: 'showProjectPanel', visible: true }
      });
      dispatch({
        type: 'SET_PANEL_VISIBILITY',
        payload: { panel: 'showTextEditPanel', visible: true }
      });
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-lg ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-gray-200">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Settings</h2>
          <p className="text-gray-600 mt-1">Configure your ho-trans experience</p>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
            title="Close"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
      </div>

      <div className="flex h-96">
        {/* Settings Navigation */}
        <div className="w-48 border-r border-gray-200 p-4">
          <nav className="space-y-2">
            {[
              { id: 'general', label: 'General', icon: '⚙️' },
              { id: 'appearance', label: 'Appearance', icon: '🎨' },
              { id: 'canvas', label: 'Canvas', icon: '🖼️' },
              { id: 'providers', label: 'LLM Providers', icon: '🤖' },
              { id: 'data', label: 'Data & Storage', icon: '💾' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`w-full flex items-center space-x-3 px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                  activeTab === tab.id
                    ? 'bg-blue-50 text-blue-700 border border-blue-200'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
              >
                <span className="text-lg">{tab.icon}</span>
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>

          {/* Reset Button */}
          <div className="mt-6 pt-4 border-t border-gray-200">
            <button
              onClick={resetAllSettings}
              className="w-full text-sm text-red-600 hover:text-red-700 hover:bg-red-50 px-3 py-2 rounded-lg transition-colors"
            >
              Reset All Settings
            </button>
          </div>
        </div>

        {/* Settings Content */}
        <div className="flex-1 p-6 overflow-y-auto">
          {/* General Settings */}
          {activeTab === 'general' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">General Preferences</h3>
                
                <div className="space-y-4">
                  {/* Language */}
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Language</label>
                      <p className="text-xs text-gray-500">Interface language</p>
                    </div>
                    <select
                      value={state.preferences.language}
                      onChange={(e) => handlePreferenceChange('language', e.target.value)}
                      className="text-sm border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="en">English</option>
                      <option value="ja">Japanese</option>
                      <option value="id">Indonesian</option>
                    </select>
                  </div>

                  {/* Auto-save */}
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Auto-save</label>
                      <p className="text-xs text-gray-500">Automatically save changes</p>
                    </div>
                    <button
                      onClick={() => handlePreferenceChange('autoSave', !state.preferences.autoSave)}
                      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                        state.preferences.autoSave ? 'bg-blue-600' : 'bg-gray-200'
                      }`}
                    >
                      <span
                        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                          state.preferences.autoSave ? 'translate-x-6' : 'translate-x-1'
                        }`}
                      />
                    </button>
                  </div>

                  {/* Default Font Family */}
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Default Font</label>
                      <p className="text-xs text-gray-500">Default font for new text regions</p>
                    </div>
                    <select
                      value={state.preferences.defaultFontFamily}
                      onChange={(e) => handlePreferenceChange('defaultFontFamily', e.target.value)}
                      className="text-sm border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="Arial">Arial</option>
                      <option value="Helvetica">Helvetica</option>
                      <option value="Times New Roman">Times New Roman</option>
                      <option value="Georgia">Georgia</option>
                      <option value="Verdana">Verdana</option>
                    </select>
                  </div>

                  {/* Default Font Size */}
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Default Font Size</label>
                      <p className="text-xs text-gray-500">Default size for new text regions</p>
                    </div>
                    <input
                      type="number"
                      min="8"
                      max="72"
                      value={state.preferences.defaultFontSize}
                      onChange={(e) => handlePreferenceChange('defaultFontSize', parseInt(e.target.value) || 14)}
                      className="w-20 text-sm border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Appearance Settings */}
          {activeTab === 'appearance' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Appearance</h3>
                
                <div className="space-y-4">
                  {/* Theme */}
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Theme</label>
                      <p className="text-xs text-gray-500">Application appearance</p>
                    </div>
                    <select
                      value={state.preferences.theme}
                      onChange={(e) => handlePreferenceChange('theme', e.target.value)}
                      className="text-sm border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="light">Light</option>
                      <option value="dark">Dark</option>
                    </select>
                  </div>

                  {/* Default Colors */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <label className="text-sm font-medium text-gray-700">Default Text Color</label>
                        <p className="text-xs text-gray-500">Default color for new text</p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="color"
                          value={state.preferences.defaultFontColor}
                          onChange={(e) => handlePreferenceChange('defaultFontColor', e.target.value)}
                          className="w-8 h-8 border border-gray-300 rounded cursor-pointer"
                        />
                        <input
                          type="text"
                          value={state.preferences.defaultFontColor}
                          onChange={(e) => handlePreferenceChange('defaultFontColor', e.target.value)}
                          className="w-20 text-xs border border-gray-300 rounded px-2 py-1"
                        />
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <label className="text-sm font-medium text-gray-700">Default Background</label>
                        <p className="text-xs text-gray-500">Default background for text regions</p>
                      </div>
                      <select
                        value={state.preferences.defaultBackgroundColor}
                        onChange={(e) => handlePreferenceChange('defaultBackgroundColor', e.target.value)}
                        className="text-sm border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="transparent">Transparent</option>
                        <option value="#FFFFFF">White</option>
                        <option value="#000000">Black</option>
                        <option value="#FFFF00">Yellow</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Canvas Settings */}
          {activeTab === 'canvas' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Canvas Settings</h3>
                
                <div className="space-y-4">
                  {/* Grid Size */}
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Grid Size</label>
                      <p className="text-xs text-gray-500">Canvas grid spacing in pixels</p>
                    </div>
                    <input
                      type="number"
                      min="10"
                      max="50"
                      value={state.preferences.gridSize}
                      onChange={(e) => handlePreferenceChange('gridSize', parseInt(e.target.value) || 20)}
                      className="w-20 text-sm border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  {/* Panel Visibility */}
                  <div className="space-y-3">
                    <h4 className="text-sm font-medium text-gray-700">Panel Visibility</h4>
                    
                    <div className="space-y-2">
                      {[
                        { key: 'showProjectPanel', label: 'Project Panel', description: 'Show project navigation panel' },
                        { key: 'showTextEditPanel', label: 'Text Editor Panel', description: 'Show text region editor panel' },
                        { key: 'showGrid', label: 'Canvas Grid', description: 'Show grid overlay on canvas' },
                        { key: 'showRulers', label: 'Canvas Rulers', description: 'Show measurement rulers' }
                      ].map((panel) => (
                        <div key={panel.key} className="flex items-center justify-between">
                          <div>
                            <label className="text-sm font-medium text-gray-700">{panel.label}</label>
                            <p className="text-xs text-gray-500">{panel.description}</p>
                          </div>
                          <button
                            onClick={() => handleUIChange(panel.key, !state.ui[panel.key as keyof typeof state.ui])}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              state.ui[panel.key as keyof typeof state.ui] ? 'bg-blue-600' : 'bg-gray-200'
                            }`}
                          >
                            <span
                              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                                state.ui[panel.key as keyof typeof state.ui] ? 'translate-x-6' : 'translate-x-1'
                              }`}
                            />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* LLM Providers */}
          {activeTab === 'providers' && (
            <LLMProviderSettings className="border-0 shadow-none p-0" />
          )}

          {/* Data Management */}
          {activeTab === 'data' && (
            <DataManagement className="border-0 shadow-none p-0" />
          )}
        </div>
      </div>
    </div>
  );
};
