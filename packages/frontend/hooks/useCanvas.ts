'use client';

import { useState, useCallback, useRef } from 'react';
import * as fabric from 'fabric';
import {
  CanvasState,
  CanvasTool,
  CanvasImageInfo,
  CanvasTextRegion,
  CanvasConfig,
  DEFAULT_CANVAS_CONFIG,
  DEFAULT_FONT_CONFIG
} from '@/types/canvas';
import { TextRegionType } from '@/types/api';

interface UseCanvasOptions {
  config?: Partial<CanvasConfig>;
  onRegionCreate?: (region: Partial<CanvasTextRegion>) => void;
  onRegionUpdate?: (regionId: string, updates: Partial<CanvasTextRegion>) => void;
  onRegionDelete?: (regionId: string) => void;
  onRegionSelect?: (regionId: string) => void;
}

export const useCanvas = (options: UseCanvasOptions = {}) => {
  const canvasRef = useRef<fabric.Canvas | null>(null);
  const imageRef = useRef<fabric.Image | null>(null);
  const regionsRef = useRef<Map<string, fabric.Rect>>(new Map());

  const [canvasState, setCanvasState] = useState<CanvasState>({
    zoom: 1,
    panX: 0,
    panY: 0,
    isDrawing: false,
    selectedTool: CanvasTool.SELECT
  });

  const [imageInfo, setImageInfo] = useState<CanvasImageInfo | null>(null);
  const [textRegions, setTextRegions] = useState<CanvasTextRegion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const config = { ...DEFAULT_CANVAS_CONFIG, ...options.config };

  // Initialize canvas
  const initializeCanvas = useCallback((canvas: fabric.Canvas) => {
    canvasRef.current = canvas;

    // Set up canvas event listeners for text region creation
    // We'll set up the handlers dynamically to avoid stale closure issues
    const mouseDownHandler = (opt: any) => {
      if (canvasState.selectedTool !== CanvasTool.TEXT_REGION || !canvasRef.current) return;

      const pointer = canvasRef.current.getPointer(opt.e);
      const rect = new fabric.Rect({
        left: pointer.x,
        top: pointer.y,
        width: 0,
        height: 0,
        fill: 'transparent',
        stroke: config.defaultRegionColor,
        strokeWidth: config.regionBorderWidth,
        strokeDashArray: [5, 5],
        selectable: false,
        evented: false
      });

      canvasRef.current.add(rect);
      (canvasRef.current as any).isDrawing = true;
      (canvasRef.current as any).drawingRect = rect;
      (canvasRef.current as any).startPointer = pointer;

      setCanvasState(prev => ({ ...prev, isDrawing: true }));
    };

    const mouseMoveHandler = (opt: any) => {
      if (!(canvasRef.current as any)?.isDrawing || !(canvasRef.current as any).drawingRect) return;

      const pointer = canvasRef.current!.getPointer(opt.e);
      const startPointer = (canvasRef.current as any).startPointer;

      if (startPointer) {
        const width = Math.abs(pointer.x - startPointer.x);
        const height = Math.abs(pointer.y - startPointer.y);
        const left = Math.min(pointer.x, startPointer.x);
        const top = Math.min(pointer.y, startPointer.y);

        (canvasRef.current as any).drawingRect.set({
          left,
          top,
          width,
          height
        });

        canvasRef.current!.renderAll();
      }
    };

    const mouseUpHandler = () => {
      if (!(canvasRef.current as any)?.isDrawing || !(canvasRef.current as any).drawingRect) return;

      const rect = (canvasRef.current as any).drawingRect;
      const width = rect.width || 0;
      const height = rect.height || 0;

      // Only create region if it has meaningful size
      if (width > 10 && height > 10) {
        const regionData = convertCanvasToNormalized(rect);

        // Create new text region
        const newRegion: Partial<CanvasTextRegion> = {
          region_type: TextRegionType.SPEECH_BUBBLE,
          x: regionData.x,
          y: regionData.y,
          width: regionData.width,
          height: regionData.height,
          isSelected: false,
          isEditing: false,
          borderColor: config.defaultRegionColor,
          fillOpacity: config.regionFillOpacity
        };

        options.onRegionCreate?.(newRegion);
      }

      // Clean up drawing state
      canvasRef.current!.remove((canvasRef.current as any).drawingRect);
      (canvasRef.current as any).isDrawing = false;
      (canvasRef.current as any).drawingRect = null;
      (canvasRef.current as any).startPointer = null;

      setCanvasState(prev => ({ ...prev, isDrawing: false }));
    };

    canvas.on('mouse:down', mouseDownHandler);
    canvas.on('mouse:move', mouseMoveHandler);
    canvas.on('mouse:up', mouseUpHandler);

    // Store handlers for cleanup
    (canvas as any)._textRegionHandlers = {
      mouseDown: mouseDownHandler,
      mouseMove: mouseMoveHandler,
      mouseUp: mouseUpHandler
    };
  }, [canvasState.selectedTool, config, options.onRegionCreate]);



  // Convert canvas coordinates to normalized coordinates
  const convertCanvasToNormalized = useCallback((obj: fabric.Object) => {
    if (!imageInfo) return { x: 0, y: 0, width: 0, height: 0 };

    const left = obj.left || 0;
    const top = obj.top || 0;
    const width = (obj.width || 0) * (obj.scaleX || 1);
    const height = (obj.height || 0) * (obj.scaleY || 1);

    // Get image position and scale
    const image = imageRef.current;
    if (!image) return { x: 0, y: 0, width: 0, height: 0 };

    const imageLeft = image.left || 0;
    const imageTop = image.top || 0;
    const imageWidth = (image.width || 1) * (image.scaleX || 1);
    const imageHeight = (image.height || 1) * (image.scaleY || 1);

    return {
      x: (left - imageLeft) / imageWidth,
      y: (top - imageTop) / imageHeight,
      width: width / imageWidth,
      height: height / imageHeight
    };
  }, [imageInfo]);

  // Convert normalized coordinates to canvas coordinates
  const convertNormalizedToCanvas = useCallback((region: { x: number; y: number; width: number; height: number }) => {
    if (!imageInfo || !imageRef.current) return { left: 0, top: 0, width: 0, height: 0 };

    const image = imageRef.current;
    const imageLeft = image.left || 0;
    const imageTop = image.top || 0;
    const imageWidth = (image.width || 1) * (image.scaleX || 1);
    const imageHeight = (image.height || 1) * (image.scaleY || 1);

    return {
      left: imageLeft + region.x * imageWidth,
      top: imageTop + region.y * imageHeight,
      width: region.width * imageWidth,
      height: region.height * imageHeight
    };
  }, [imageInfo]);

  // Add text region to canvas
  const addTextRegion = useCallback((region: CanvasTextRegion) => {
    if (!canvasRef.current) return;

    const canvasCoords = convertNormalizedToCanvas(region);

    const rect = new fabric.Rect({
      left: canvasCoords.left,
      top: canvasCoords.top,
      width: canvasCoords.width,
      height: canvasCoords.height,
      fill: region.background_color === 'transparent' ? 'transparent' : region.background_color || 'rgba(59, 130, 246, 0.2)',
      stroke: region.isSelected ? config.selectedRegionColor : region.borderColor,
      strokeWidth: config.regionBorderWidth,
      selectable: true,
      evented: true,
      data: { regionId: region.id }
    });

    // Add text if available
    if (region.translated_text || region.original_text) {
      const text = new fabric.Text(region.translated_text || region.original_text || '', {
        left: canvasCoords.left + canvasCoords.width / 2,
        top: canvasCoords.top + canvasCoords.height / 2,
        fontSize: region.font_size || DEFAULT_FONT_CONFIG.size,
        fontFamily: region.font_family || DEFAULT_FONT_CONFIG.family,
        fill: region.font_color || DEFAULT_FONT_CONFIG.color,
        textAlign: 'center',
        originX: 'center',
        originY: 'center',
        selectable: false,
        evented: false,
        data: { regionId: region.id, type: 'text' }
      });

      canvasRef.current.add(text);
    }

    canvasRef.current.add(rect);
    regionsRef.current.set(region.id, rect);

    // Set up event handlers
    rect.on('selected', () => {
      options.onRegionSelect?.(region.id);
    });

    rect.on('modified', () => {
      const updatedCoords = convertCanvasToNormalized(rect);
      options.onRegionUpdate?.(region.id, updatedCoords);
    });

    canvasRef.current.renderAll();
  }, [config, convertNormalizedToCanvas, convertCanvasToNormalized, options]);

  // Remove text region from canvas
  const removeTextRegion = useCallback((regionId: string) => {
    if (!canvasRef.current) return;

    const rect = regionsRef.current.get(regionId);
    if (rect) {
      canvasRef.current.remove(rect);
      regionsRef.current.delete(regionId);
    }

    // Remove associated text
    const objects = canvasRef.current.getObjects();
    objects.forEach(obj => {
      if ((obj as any).data?.regionId === regionId && (obj as any).data?.type === 'text') {
        canvasRef.current?.remove(obj);
      }
    });

    canvasRef.current.renderAll();
  }, []);

  // Update text region
  const updateTextRegion = useCallback((regionId: string, updates: Partial<CanvasTextRegion>) => {
    if (!canvasRef.current) return;

    const rect = regionsRef.current.get(regionId);
    if (!rect) return;

    // Update visual properties
    if (updates.borderColor) {
      rect.set('stroke', updates.borderColor);
    }

    if (updates.background_color !== undefined) {
      rect.set('fill', updates.background_color === 'transparent' ? 'transparent' : updates.background_color);
    }

    // Update position and size if provided
    if (updates.x !== undefined || updates.y !== undefined || updates.width !== undefined || updates.height !== undefined) {
      const currentRegion = textRegions.find(r => r.id === regionId);
      if (currentRegion) {
        const newRegion = { ...currentRegion, ...updates };
        const canvasCoords = convertNormalizedToCanvas(newRegion);

        rect.set({
          left: canvasCoords.left,
          top: canvasCoords.top,
          width: canvasCoords.width,
          height: canvasCoords.height
        });
      }
    }

    // Update text content and styling
    if (updates.translated_text !== undefined || updates.font_family || updates.font_size || updates.font_color) {
      const objects = canvasRef.current.getObjects();
      const textObj = objects.find(obj => (obj as any).data?.regionId === regionId && (obj as any).data?.type === 'text') as fabric.Text;

      if (textObj) {
        if (updates.translated_text !== undefined) {
          textObj.set('text', updates.translated_text);
        }
        if (updates.font_family) {
          textObj.set('fontFamily', updates.font_family);
        }
        if (updates.font_size) {
          textObj.set('fontSize', updates.font_size);
        }
        if (updates.font_color) {
          textObj.set('fill', updates.font_color);
        }
      }
    }

    canvasRef.current.renderAll();
  }, [textRegions, convertNormalizedToCanvas]);

  // Canvas control methods
  const zoomIn = useCallback(() => {
    if (!canvasRef.current) return;

    const zoom = Math.min(config.maxZoom, canvasRef.current.getZoom() + config.zoomStep);
    canvasRef.current.setZoom(zoom);
    setCanvasState(prev => ({ ...prev, zoom }));
  }, [config]);

  const zoomOut = useCallback(() => {
    if (!canvasRef.current) return;

    const zoom = Math.max(config.minZoom, canvasRef.current.getZoom() - config.zoomStep);
    canvasRef.current.setZoom(zoom);
    setCanvasState(prev => ({ ...prev, zoom }));
  }, [config]);

  const resetZoom = useCallback(() => {
    if (!canvasRef.current) return;

    canvasRef.current.setZoom(1);
    canvasRef.current.setViewportTransform([1, 0, 0, 1, 0, 0]);
    setCanvasState(prev => ({ ...prev, zoom: 1, panX: 0, panY: 0 }));
  }, []);

  const setTool = useCallback((tool: CanvasTool) => {
    if (!canvasRef.current) return;

    canvasRef.current.selection = tool === CanvasTool.SELECT;
    setCanvasState(prev => ({ ...prev, selectedTool: tool }));
  }, []);

  // Update canvas state
  const updateCanvasState = useCallback((updates: Partial<CanvasState>) => {
    setCanvasState(prev => ({ ...prev, ...updates }));
  }, []);

  return {
    // State
    canvasState,
    imageInfo,
    textRegions,
    isLoading,
    error,

    // Canvas management
    initializeCanvas,
    updateCanvasState,

    // Image management
    setImageInfo,
    setIsLoading,
    setError,

    // Text region management
    addTextRegion,
    removeTextRegion,
    updateTextRegion,
    setTextRegions,

    // Canvas controls
    zoomIn,
    zoomOut,
    resetZoom,
    setTool,

    // Utility functions
    convertCanvasToNormalized,
    convertNormalizedToCanvas,

    // Refs
    canvasRef,
    imageRef,
    regionsRef
  };
};

export default useCanvas;
