'use client';

import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { CanvasState, CanvasTool, CanvasImageInfo } from '@/types/canvas';

interface CanvasStore extends CanvasState {
  // Image info
  imageInfo: CanvasImageInfo | null;

  // Loading and error states
  isLoading: boolean;
  error: string | null;

  // Actions for canvas state
  setZoom: (zoom: number) => void;
  setPan: (panX: number, panY: number) => void;
  setTool: (tool: CanvasTool) => void;
  setDrawing: (isDrawing: boolean) => void;
  setSelectedRegion: (regionId?: string) => void;

  // Actions for image info
  setImageInfo: (imageInfo: CanvasImageInfo | null) => void;

  // Actions for loading and error states
  setLoading: (isLoading: boolean) => void;
  setError: (error: string | null) => void;

  // Optimized update methods to reduce re-renders
  updateCanvasState: (updates: Partial<CanvasState>) => void;

  // Zoom controls
  zoomIn: () => void;
  zoomOut: () => void;
  resetZoom: () => void;

  // Reset store
  reset: () => void;
}

const initialCanvasState: CanvasState = {
  zoom: 1,
  panX: 0,
  panY: 0,
  isDrawing: false,
  selectedTool: CanvasTool.SELECT,
  selectedRegion: undefined
};

export const useCanvasStore = create<CanvasStore>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    ...initialCanvasState,
    imageInfo: null,
    isLoading: false,
    error: null,

    // Canvas state actions
    setZoom: (zoom: number) => {
      // Clamp zoom to reasonable bounds
      const clampedZoom = Math.max(0.1, Math.min(5.0, zoom));
      set({ zoom: clampedZoom });
    },

    setPan: (panX: number, panY: number) => {
      set({ panX, panY });
    },

    setTool: (tool: CanvasTool) => {
      set({ selectedTool: tool });
    },

    setDrawing: (isDrawing: boolean) => {
      set({ isDrawing });
    },

    setSelectedRegion: (regionId?: string) => {
      set({ selectedRegion: regionId });
    },

    // Image info actions
    setImageInfo: (imageInfo: CanvasImageInfo | null) => {
      set({ imageInfo });
    },

    // Loading and error actions
    setLoading: (isLoading: boolean) => {
      set({ isLoading });
    },

    setError: (error: string | null) => {
      set({ error });
    },

    // Optimized batch update
    updateCanvasState: (updates: Partial<CanvasState>) => {
      set((state) => ({
        ...state,
        ...updates
      }));
    },

    // Zoom controls with optimized updates
    zoomIn: () => {
      const { zoom, setZoom } = get();
      setZoom(zoom + 0.1);
    },

    zoomOut: () => {
      const { zoom, setZoom } = get();
      setZoom(zoom - 0.1);
    },

    resetZoom: () => {
      set({ zoom: 1, panX: 0, panY: 0 });
    },

    // Reset store to initial state
    reset: () => {
      set({
        ...initialCanvasState,
        imageInfo: null,
        isLoading: false,
        error: null
      });
    }
  }))
);

// Optimized selectors to prevent unnecessary re-renders
export const useCanvasZoom = () => useCanvasStore((state) => state.zoom);
export const useCanvasPan = () => useCanvasStore((state) => ({ panX: state.panX, panY: state.panY }));
export const useCanvasTool = () => useCanvasStore((state) => state.selectedTool);
export const useCanvasDrawing = () => useCanvasStore((state) => state.isDrawing);
export const useCanvasSelectedRegion = () => useCanvasStore((state) => state.selectedRegion);
export const useCanvasImageInfo = () => useCanvasStore((state) => state.imageInfo);
export const useCanvasLoading = () => useCanvasStore((state) => state.isLoading);
export const useCanvasError = () => useCanvasStore((state) => state.error);

// Combined selectors for components that need multiple values
export const useCanvasState = () => useCanvasStore((state) => ({
  zoom: state.zoom,
  panX: state.panX,
  panY: state.panY,
  isDrawing: state.isDrawing,
  selectedTool: state.selectedTool,
  selectedRegion: state.selectedRegion
}));

export const useCanvasActions = () => useCanvasStore((state) => ({
  setZoom: state.setZoom,
  setPan: state.setPan,
  setTool: state.setTool,
  setDrawing: state.setDrawing,
  setSelectedRegion: state.setSelectedRegion,
  updateCanvasState: state.updateCanvasState,
  zoomIn: state.zoomIn,
  zoomOut: state.zoomOut,
  resetZoom: state.resetZoom
}));