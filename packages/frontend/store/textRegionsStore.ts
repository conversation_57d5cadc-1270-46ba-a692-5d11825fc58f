'use client';

import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { CanvasTextRegion } from '@/types/canvas';

interface TextRegionsStore {
  // State
  textRegions: CanvasTextRegion[];
  selectedRegionIds: string[];
  
  // Actions
  setTextRegions: (regions: CanvasTextRegion[]) => void;
  addTextRegion: (region: CanvasTextRegion) => void;
  updateTextRegion: (regionId: string, updates: Partial<CanvasTextRegion>) => void;
  removeTextRegion: (regionId: string) => void;
  
  // Selection actions
  selectRegion: (regionId: string) => void;
  selectMultipleRegions: (regionIds: string[]) => void;
  clearSelection: () => void;
  toggleRegionSelection: (regionId: string) => void;
  
  // Batch operations for performance
  batchUpdateRegions: (updates: Array<{ id: string; updates: Partial<CanvasTextRegion> }>) => void;
  batchRemoveRegions: (regionIds: string[]) => void;
  
  // Utility functions
  getRegionById: (regionId: string) => CanvasTextRegion | undefined;
  getSelectedRegions: () => CanvasTextRegion[];
  
  // Reset store
  reset: () => void;
}

export const useTextRegionsStore = create<TextRegionsStore>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    textRegions: [],
    selectedRegionIds: [],
    
    // Basic CRUD operations
    setTextRegions: (regions: CanvasTextRegion[]) => {
      set({ textRegions: regions });
    },
    
    addTextRegion: (region: CanvasTextRegion) => {
      set((state) => ({
        textRegions: [...state.textRegions, region]
      }));
    },
    
    updateTextRegion: (regionId: string, updates: Partial<CanvasTextRegion>) => {
      set((state) => ({
        textRegions: state.textRegions.map((region) =>
          region.id === regionId ? { ...region, ...updates } : region
        )
      }));
    },
    
    removeTextRegion: (regionId: string) => {
      set((state) => ({
        textRegions: state.textRegions.filter((region) => region.id !== regionId),
        selectedRegionIds: state.selectedRegionIds.filter((id) => id !== regionId)
      }));
    },
    
    // Selection operations
    selectRegion: (regionId: string) => {
      set({ selectedRegionIds: [regionId] });
    },
    
    selectMultipleRegions: (regionIds: string[]) => {
      set({ selectedRegionIds: regionIds });
    },
    
    clearSelection: () => {
      set({ selectedRegionIds: [] });
    },
    
    toggleRegionSelection: (regionId: string) => {
      set((state) => {
        const isSelected = state.selectedRegionIds.includes(regionId);
        if (isSelected) {
          return {
            selectedRegionIds: state.selectedRegionIds.filter((id) => id !== regionId)
          };
        } else {
          return {
            selectedRegionIds: [...state.selectedRegionIds, regionId]
          };
        }
      });
    },
    
    // Batch operations for performance optimization
    batchUpdateRegions: (updates: Array<{ id: string; updates: Partial<CanvasTextRegion> }>) => {
      set((state) => {
        const updatesMap = new Map(updates.map(({ id, updates }) => [id, updates]));
        return {
          textRegions: state.textRegions.map((region) => {
            const regionUpdates = updatesMap.get(region.id);
            return regionUpdates ? { ...region, ...regionUpdates } : region;
          })
        };
      });
    },
    
    batchRemoveRegions: (regionIds: string[]) => {
      const regionIdsSet = new Set(regionIds);
      set((state) => ({
        textRegions: state.textRegions.filter((region) => !regionIdsSet.has(region.id)),
        selectedRegionIds: state.selectedRegionIds.filter((id) => !regionIdsSet.has(id))
      }));
    },
    
    // Utility functions
    getRegionById: (regionId: string) => {
      const { textRegions } = get();
      return textRegions.find((region) => region.id === regionId);
    },
    
    getSelectedRegions: () => {
      const { textRegions, selectedRegionIds } = get();
      return textRegions.filter((region) => selectedRegionIds.includes(region.id));
    },
    
    // Reset store
    reset: () => {
      set({
        textRegions: [],
        selectedRegionIds: []
      });
    }
  }))
);

// Optimized selectors
export const useTextRegions = () => useTextRegionsStore((state) => state.textRegions);
export const useSelectedRegionIds = () => useTextRegionsStore((state) => state.selectedRegionIds);
export const useSelectedRegions = () => useTextRegionsStore((state) => state.getSelectedRegions());

// Selector for specific region
export const useTextRegion = (regionId: string) => 
  useTextRegionsStore((state) => state.textRegions.find(region => region.id === regionId));

// Actions selector
export const useTextRegionsActions = () => useTextRegionsStore((state) => ({
  setTextRegions: state.setTextRegions,
  addTextRegion: state.addTextRegion,
  updateTextRegion: state.updateTextRegion,
  removeTextRegion: state.removeTextRegion,
  selectRegion: state.selectRegion,
  selectMultipleRegions: state.selectMultipleRegions,
  clearSelection: state.clearSelection,
  toggleRegionSelection: state.toggleRegionSelection,
  batchUpdateRegions: state.batchUpdateRegions,
  batchRemoveRegions: state.batchRemoveRegions,
  getRegionById: state.getRegionById,
  getSelectedRegions: state.getSelectedRegions
}));
